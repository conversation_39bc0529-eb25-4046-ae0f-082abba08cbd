* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('img/bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    color: #fff;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
}

.logo-container h1 {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #fff;
    opacity: 0.9;
}

.stat i {
    color: #4CAF50;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.step {
    display: none;
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.step.active {
    display: block;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 30px;
}

.step-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.step-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

/* Input Container */
.input-container {
    margin-bottom: 30px;
}

.instagram-url {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.instagram-url:focus-within {
    border-color: #e6683c;
    background: rgba(255, 255, 255, 0.15);
}

.url-prefix {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    white-space: nowrap;
}

.username-input {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 16px;
    flex: 1;
    padding-left: 5px;
}

.username-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Buttons */
.btn-primary {
    width: 100%;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743);
    border: none;
    border-radius: 12px;
    padding: 15px 20px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(240, 148, 51, 0.4);
}

.btn-primary.disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Features */
.features {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.feature i {
    font-size: 24px;
    color: #4CAF50;
    margin-bottom: 5px;
}

.feature span {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* Profile Info */
.profile-info {
    margin-bottom: 30px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.profile-check-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #4CAF50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.profile-check-icon i {
    font-size: 35px;
    color: white;
    animation: checkPulse 2s infinite;
}

@keyframes checkPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.profile-details h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.profile-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
    font-weight: 500;
}

.verified-badge {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
}

/* Follower Selection */
.follower-selection h3 {
    text-align: center;
    margin-bottom: 10px;
    font-size: 22px;
}

.follower-selection p {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 25px;
}

.follower-input-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.follower-input-container:focus-within {
    border-color: #e6683c;
    background: rgba(255, 255, 255, 0.15);
}

.follower-icon {
    color: #e6683c;
    margin-right: 10px;
    font-size: 18px;
}

.follower-input {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.follower-label {
    color: rgba(255, 255, 255, 0.7);
    margin-left: 10px;
}

.quick-select {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
}

.quick-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover, .quick-btn.active {
    border-color: #e6683c;
    background: rgba(230, 104, 60, 0.2);
}

/* Live Activity Cards */
.live-activity {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.live-activity h4 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.live-activity h4 i {
    color: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.activity-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 200px;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.activity-card {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideInUp 0.5s ease-out;
    transition: all 0.3s ease;
}

.activity-card:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.activity-card .card-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #4CAF50;
    margin-right: 12px;
}

.activity-card .card-info {
    flex: 1;
}

.activity-card .card-username {
    font-weight: 600;
    font-size: 14px;
    color: #fff;
    margin-bottom: 2px;
}

.activity-card .card-action {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.activity-card .card-amount {
    font-weight: 600;
    color: #4CAF50;
    font-size: 13px;
    white-space: nowrap;
}

/* Step 3 - Loading Messages */
.loading-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.loading-icon {
    margin-bottom: 40px;
}

.spinner-large {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(255, 255, 255, 0.3);
    border-top: 6px solid #e6683c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.message-container {
    max-width: 500px;
    width: 100%;
}

.loading-message {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 4px solid #e6683c;
    opacity: 0;
    transform: translateY(20px);
    animation: messageSlideIn 0.5s ease-out forwards;
}

@keyframes messageSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-message.final {
    background: rgba(255, 152, 0, 0.2);
    border-left-color: #ff9800;
    font-weight: 600;
    color: #ff9800;
}

.loading-message p {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
}

/* Step 4 - SMS Verification */
.verification-header {
    text-align: center;
    margin-bottom: 40px;
}

.warning-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff9800, #f57c00);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    border: 3px solid #ff9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.3);
}

.warning-icon i {
    font-size: 35px;
    color: white;
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.verification-header h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #ff9800;
}

.verification-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.5;
}

.sms-verification {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    margin-bottom: 40px;
}

.countdown-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.countdown-timer {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px 30px;
    border: 2px solid rgba(231, 76, 60, 0.3);
}

.heartbeat {
    color: #e74c3c;
    font-size: 24px;
    animation: heartbeat 1.5s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.countdown-timer span {
    font-size: 28px;
    font-weight: 700;
    color: #e74c3c;
    min-width: 80px;
}

.countdown-section p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.sms-verification-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.sms-verification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}





/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #fff;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #e6683c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-stats {
        justify-content: center;
    }

    .step {
        padding: 30px 20px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .verified-badge {
        margin-left: 0;
    }

    /* Step 3 - Loading Messages Mobile */
    .loading-messages {
        min-height: 300px;
        padding: 20px 0;
    }

    .spinner-large {
        width: 60px;
        height: 60px;
        border-width: 4px;
    }

    .loading-message {
        padding: 15px;
        margin-bottom: 12px;
    }

    .loading-message p {
        font-size: 14px;
    }

    /* Step 4 - SMS Verification Mobile */
    .verification-header h2 {
        font-size: 20px;
    }

    .verification-header p {
        font-size: 14px;
    }

    .warning-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;
    }

    .warning-icon i {
        font-size: 28px;
    }

    .sms-verification {
        gap: 25px;
        margin-bottom: 30px;
    }

    .countdown-timer {
        gap: 12px;
        padding: 15px 20px;
        flex-direction: column;
        text-align: center;
    }

    .countdown-timer span {
        font-size: 24px;
        min-width: auto;
    }

    .heartbeat {
        font-size: 20px;
    }

    .sms-verification-btn {
        padding: 12px 25px;
        font-size: 16px;
        width: 100%;
        max-width: 280px;
    }

    /* Activity Cards Mobile */
    .activity-card {
        padding: 10px 12px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .activity-card .card-avatar {
        width: 30px;
        height: 30px;
        margin-right: 0;
        margin-bottom: 5px;
    }

    .activity-card .card-info {
        order: 2;
    }

    .activity-card .card-amount {
        order: 1;
        font-size: 12px;
    }

    .activity-card .card-username {
        font-size: 13px;
    }

    .activity-card .card-action {
        font-size: 11px;
    }

    /* Quick Select Buttons Mobile */
    .quick-select {
        flex-wrap: wrap;
        gap: 8px;
    }

    .quick-btn {
        flex: 1;
        min-width: calc(50% - 4px);
        padding: 8px;
        font-size: 14px;
    }

    /* Input Containers Mobile */
    .instagram-url {
        padding: 12px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .url-prefix {
        font-size: 14px;
    }

    .username-input {
        font-size: 16px;
        text-align: center;
        padding: 0;
    }

    .follower-input-container {
        padding: 12px;
    }

    .follower-input {
        font-size: 16px;
    }

    /* Features Mobile */
    .features {
        flex-direction: column;
        gap: 15px;
        margin-top: 25px;
    }

    .feature {
        flex-direction: row;
        gap: 12px;
    }

    .feature i {
        font-size: 20px;
    }

    .feature span {
        font-size: 13px;
    }
}
