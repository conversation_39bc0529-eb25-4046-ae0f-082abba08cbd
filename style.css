* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('img/bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    color: #fff;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
}

.logo-container h1 {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #fff;
    opacity: 0.9;
}

.stat i {
    color: #4CAF50;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.step {
    display: none;
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.step.active {
    display: block;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 30px;
}

.step-header h2 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
}

.step-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

/* Input Container */
.input-container {
    margin-bottom: 30px;
}

.instagram-url {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.instagram-url:focus-within {
    border-color: #e6683c;
    background: rgba(255, 255, 255, 0.15);
}

.url-prefix {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    white-space: nowrap;
}

.username-input {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 16px;
    flex: 1;
    padding-left: 5px;
}

.username-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Buttons */
.btn-primary {
    width: 100%;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743);
    border: none;
    border-radius: 12px;
    padding: 15px 20px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(240, 148, 51, 0.4);
}

.btn-primary.disabled {
    background: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Features */
.features {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.feature i {
    font-size: 24px;
    color: #4CAF50;
    margin-bottom: 5px;
}

.feature span {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* Profile Info */
.profile-info {
    margin-bottom: 30px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.profile-check-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #4CAF50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.profile-check-icon i {
    font-size: 35px;
    color: white;
    animation: checkPulse 2s infinite;
}

@keyframes checkPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.profile-details h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.profile-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
    font-weight: 500;
}

.verified-badge {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4CAF50;
    font-size: 14px;
}

/* Follower Selection */
.follower-selection h3 {
    text-align: center;
    margin-bottom: 10px;
    font-size: 22px;
}

.follower-selection p {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 25px;
}

.follower-input-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.follower-input-container:focus-within {
    border-color: #e6683c;
    background: rgba(255, 255, 255, 0.15);
}

.follower-icon {
    color: #e6683c;
    margin-right: 10px;
    font-size: 18px;
}

.follower-input {
    background: none;
    border: none;
    outline: none;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.follower-label {
    color: rgba(255, 255, 255, 0.7);
    margin-left: 10px;
}

.quick-select {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
}

.quick-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover, .quick-btn.active {
    border-color: #e6683c;
    background: rgba(230, 104, 60, 0.2);
}

/* Live Activity Cards */
.live-activity {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.live-activity h4 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.live-activity h4 i {
    color: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.activity-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 200px;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.activity-card {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideInUp 0.5s ease-out;
    transition: all 0.3s ease;
}

.activity-card:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.activity-card .card-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #4CAF50;
    margin-right: 12px;
}

.activity-card .card-info {
    flex: 1;
}

.activity-card .card-username {
    font-weight: 600;
    font-size: 14px;
    color: #fff;
    margin-bottom: 2px;
}

.activity-card .card-action {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.activity-card .card-amount {
    font-weight: 600;
    color: #4CAF50;
    font-size: 13px;
    white-space: nowrap;
}

/* Step 3 - Verification */
.verification-header {
    text-align: center;
    margin-bottom: 30px;
}

.warning-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff9800, #f57c00);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    border: 3px solid #ff9800;
    box-shadow: 0 0 20px rgba(255, 152, 0, 0.3);
}

.warning-icon i {
    font-size: 35px;
    color: white;
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.verification-header h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #ff9800;
}

.verification-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.5;
}

.verification-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

.verification-step {
    display: flex;
    align-items: flex-start;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.verification-step:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #e6683c, #dc2743);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.step-icon.youtube {
    background: linear-gradient(45deg, #ff0000, #cc0000);
}

.step-icon.twitter {
    background: linear-gradient(45deg, #1da1f2, #0d8bd9);
}

.step-icon.sms {
    background: linear-gradient(45deg, #4CAF50, #45a049);
}

.step-icon i {
    font-size: 24px;
    color: white;
}

.step-text {
    flex: 1;
}

.step-text h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #fff;
}

.step-text p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.step-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.step-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.youtube-btn {
    background: linear-gradient(45deg, #ff0000, #cc0000);
}

.youtube-btn:hover {
    box-shadow: 0 5px 15px rgba(255, 0, 0, 0.4);
}

.twitter-btn {
    background: linear-gradient(45deg, #1da1f2, #0d8bd9);
}

.twitter-btn:hover {
    box-shadow: 0 5px 15px rgba(29, 161, 242, 0.4);
}

.countdown-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 10px 0;
}

.countdown-timer {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.heartbeat {
    color: #e74c3c;
    animation: heartbeat 1.5s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.countdown-timer span {
    font-size: 18px;
    font-weight: 700;
    color: #e74c3c;
}

.countdown-container p {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}



/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #fff;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #e6683c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-stats {
        justify-content: center;
    }
    
    .step {
        padding: 30px 20px;
    }
    
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .verified-badge {
        margin-left: 0;
    }
}
