# Instagram Takipçi Landing Page

Modern ve profesyonel Instagram takipçi kazanma landing page'i.

## 🚀 Özellikler

- ✅ Gerçek Instagram profil verilerini çeker
- ✅ Profil resmi, kullanıcı adı ve takipçi sayısını gösterir
- ✅ Modern ve responsive tasarım
- ✅ 50.000'e kadar takipçi seçimi
- ✅ Glassmorphism UI efektleri
- ✅ Smooth animasyonlar
- ✅ Mobil uyumlu

## 📁 Dosya Yapısı

```
instagram-landing/
├── index.html          # Ana sayfa
├── style.css           # CSS stilleri
├── script.js           # JavaScript fonksiyonları
├── proxy.php           # Instagram veri çekme proxy'si
├── .htaccess           # Apache konfigürasyonu
├── README.md           # Bu dosya
└── img/
    ├── bg.jpg          # Arka plan resmi
    ├── logo.webp       # Logo
    └── takipçi.png     # Takipçi ikonu
```

## 🛠 Kurulum

### Subdomain'e Yükleme

1. **Dosyaları ZIP'le:**
   - Tüm dosyaları seç
   - <PERSON><PERSON> tık → "Sıkıştır" veya ZIP oluştur

2. **Subdomain'e Yükle:**
   - cPanel veya hosting paneline giriş yap
   - Subdomain oluştur (örn: takipci.domain.com)
   - ZIP dosyasını subdomain klasörüne yükle
   - ZIP'i çıkar

3. **Gereksinimler:**
   - PHP 7.4+ destekli hosting
   - cURL extension aktif
   - mod_rewrite aktif (Apache)

### Yerel Test

```bash
# PHP built-in server ile test
php -S localhost:8000
```

Tarayıcıda `http://localhost:8000` adresine git.

## ⚙️ Konfigürasyon

### PHP Proxy Ayarları

`proxy.php` dosyasında gerekirse şu ayarları değiştirebilirsiniz:

```php
// Timeout süresi (saniye)
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// User Agent
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0...');
```

### Takipçi Limitleri

`script.js` ve `index.html` dosyalarında limit değiştirilmiştir:
- Minimum: 100 takipçi
- Maksimum: 50.000 takipçi

## 🔧 Sorun Giderme

### Instagram Verileri Gelmiyor

1. **Hosting PHP desteği:** PHP 7.4+ gerekli
2. **cURL extension:** Aktif olmalı
3. **CORS hatası:** `.htaccess` dosyası yüklü olmalı
4. **Instagram rate limit:** Çok fazla istek gönderme

### Profil Resmi Görünmüyor

- Instagram CORS politikası nedeniyle bazen profil resimleri yüklenemez
- Bu durumda otomatik placeholder resim gösterilir

### PHP Hataları

Hosting panelinde error log'ları kontrol edin:
```
cPanel → Error Logs → Domain Error Logs
```

## 📱 Responsive Tasarım

- ✅ Desktop (1200px+)
- ✅ Tablet (768px - 1199px)
- ✅ Mobile (320px - 767px)

## 🎨 Tasarım Özellikleri

- **Renk Paleti:** Instagram gradient renkleri
- **Font:** Inter (Google Fonts)
- **İkonlar:** Font Awesome 6
- **Efektler:** Glassmorphism, smooth transitions
- **Animasyonlar:** CSS3 keyframes

## 🔒 Güvenlik

- XSS koruması
- CSRF koruması
- Input validation
- Secure headers (.htaccess)

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. README.md dosyasını kontrol edin
2. Error log'ları inceleyin
3. PHP ve hosting gereksinimlerini doğrulayın

---

**Not:** Bu proje eğitim amaçlıdır. Gerçek takipçi satışı yapmaz, sadece UI/UX gösterimi içindir.
