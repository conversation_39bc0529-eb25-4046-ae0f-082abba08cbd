// DOM Elements
const step1 = document.getElementById('step1');
const step2 = document.getElementById('step2');
const usernameInput = document.getElementById('username');
const checkProfileBtn = document.getElementById('checkProfile');
const loadingOverlay = document.getElementById('loadingOverlay');
const profilePic = document.getElementById('profilePic');
const profileUsername = document.getElementById('profileUsername');
const followerCount = document.getElementById('followerCount');
const followerAmountInput = document.getElementById('followerAmount');
const proceedBtn = document.getElementById('proceedBtn');
const quickBtns = document.querySelectorAll('.quick-btn');

// Event Listeners
checkProfileBtn.addEventListener('click', checkProfile);
usernameInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        checkProfile();
    }
});

followerAmountInput.addEventListener('input', validateFollowerAmount);

quickBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const amount = btn.dataset.amount;
        followerAmountInput.value = amount;
        
        // Remove active class from all buttons
        quickBtns.forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        btn.classList.add('active');
        
        validateFollowerAmount();
    });
});

proceedBtn.addEventListener('click', proceedToNextStep);

// Functions
async function checkProfile() {
    const username = usernameInput.value.trim();
    
    if (!username) {
        showError('Lütfen kullanıcı adınızı girin');
        return;
    }
    
    if (username.length < 3) {
        showError('Kullanıcı adı en az 3 karakter olmalıdır');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    try {
        // Simulate API call to get Instagram profile data
        await simulateProfileFetch(username);
        
        // Hide loading
        showLoading(false);
        
        // Show step 2
        showStep2();
        
    } catch (error) {
        showLoading(false);
        showError('Profil bulunamadı. Lütfen kullanıcı adını kontrol edin.');
    }
}

async function simulateProfileFetch(username) {
    // Simulate network delay for realistic experience
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock Instagram profiles with Turkish names and realistic data
    const mockProfiles = {
        'elonofficiall': {
            username: 'elonofficiall',
            profilePic: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
            followerCount: '150M'
        },
        'ahmetk': {
            username: 'ahmetk',
            profilePic: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
            followerCount: '25K'
        },
        'mahmuth': {
            username: 'mahmuth',
            profilePic: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
            followerCount: '42K'
        },
        'zeynepyilmaz': {
            username: 'zeynepyilmaz',
            profilePic: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
            followerCount: '18K'
        },
        'mustafaoz': {
            username: 'mustafaoz',
            profilePic: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
            followerCount: '33K'
        }
    };

    // Check if profile exists in our mock data
    const profile = mockProfiles[username.toLowerCase()];

    if (!profile) {
        // Generate dynamic profile for any username
        const dynamicProfile = {
            username: username,
            profilePic: `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&size=150&background=${getRandomColor()}&color=ffffff&bold=true`,
            followerCount: generateRealisticFollowerCount()
        };

        // Update profile info
        profileUsername.textContent = dynamicProfile.username;
        profilePic.src = dynamicProfile.profilePic;
        followerCount.textContent = dynamicProfile.followerCount;

        return dynamicProfile;
    }

    // Update profile info with mock data
    profileUsername.textContent = profile.username;
    profilePic.src = profile.profilePic;
    followerCount.textContent = profile.followerCount;

    return profile;
}

function getRandomColor() {
    const colors = ['e6683c', 'f09433', 'dc2743', 'cc2366', 'bc1888', '4CAF50', '2196F3', '9C27B0', 'FF5722'];
    return colors[Math.floor(Math.random() * colors.length)];
}

function generateRealisticFollowerCount() {
    const ranges = [
        { min: 1000, max: 5000, suffix: 'K', weight: 30 },
        { min: 5000, max: 15000, suffix: 'K', weight: 25 },
        { min: 15000, max: 50000, suffix: 'K', weight: 20 },
        { min: 50000, max: 100000, suffix: 'K', weight: 15 },
        { min: 100000, max: 500000, suffix: 'K', weight: 8 },
        { min: 1000000, max: 5000000, suffix: 'M', weight: 2 }
    ];

    // Weighted random selection
    const totalWeight = ranges.reduce((sum, range) => sum + range.weight, 0);
    let random = Math.random() * totalWeight;

    for (const range of ranges) {
        random -= range.weight;
        if (random <= 0) {
            const count = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;

            if (range.suffix === 'K') {
                return (count / 1000).toFixed(1) + 'K';
            } else if (range.suffix === 'M') {
                return (count / 1000000).toFixed(1) + 'M';
            }
            return count.toString();
        }
    }

    return '2.5K'; // fallback
}



function showStep2() {
    step1.classList.remove('active');
    step2.classList.add('active');
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
    // Create and show error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}

function validateFollowerAmount() {
    const amount = parseInt(followerAmountInput.value);

    if (amount >= 100 && amount <= 50000) {
        proceedBtn.classList.remove('disabled');
        proceedBtn.disabled = false;
    } else {
        proceedBtn.classList.add('disabled');
        proceedBtn.disabled = true;
    }
}

function proceedToNextStep() {
    const amount = parseInt(followerAmountInput.value);

    if (amount >= 100 && amount <= 50000) {
        // Here you would proceed to the next step
        // For now, we'll show a success message
        showSuccess(`${amount} takipçi için işlem başlatılıyor...`);
    }
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    successDiv.textContent = message;
    
    document.body.appendChild(successDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Input validation for username
usernameInput.addEventListener('input', (e) => {
    // Remove any characters that aren't allowed in Instagram usernames
    e.target.value = e.target.value.replace(/[^a-zA-Z0-9._]/g, '');
});

// Auto-focus on username input when page loads
window.addEventListener('load', () => {
    usernameInput.focus();
});
