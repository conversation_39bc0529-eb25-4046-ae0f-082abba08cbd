// DOM Elements
const step1 = document.getElementById('step1');
const step2 = document.getElementById('step2');
const usernameInput = document.getElementById('username');
const checkProfileBtn = document.getElementById('checkProfile');
const loadingOverlay = document.getElementById('loadingOverlay');
const profilePic = document.getElementById('profilePic');
const profileUsername = document.getElementById('profileUsername');
const followerCount = document.getElementById('followerCount');
const followerAmountInput = document.getElementById('followerAmount');
const proceedBtn = document.getElementById('proceedBtn');
const quickBtns = document.querySelectorAll('.quick-btn');

// Event Listeners
checkProfileBtn.addEventListener('click', checkProfile);
usernameInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        checkProfile();
    }
});

followerAmountInput.addEventListener('input', validateFollowerAmount);

quickBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const amount = btn.dataset.amount;
        followerAmountInput.value = amount;
        
        // Remove active class from all buttons
        quickBtns.forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        btn.classList.add('active');
        
        validateFollowerAmount();
    });
});

proceedBtn.addEventListener('click', proceedToNextStep);

// Functions
async function checkProfile() {
    const username = usernameInput.value.trim();
    
    if (!username) {
        showError('Lütfen kullanıcı adınızı girin');
        return;
    }
    
    if (username.length < 3) {
        showError('Kullanıcı adı en az 3 karakter olmalıdır');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    try {
        // Simulate API call to get Instagram profile data
        await simulateProfileFetch(username);
        
        // Hide loading
        showLoading(false);
        
        // Show step 2
        showStep2();
        
    } catch (error) {
        showLoading(false);
        showError('Profil bulunamadı. Lütfen kullanıcı adını kontrol edin.');
    }
}

async function simulateProfileFetch(username) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // For demo purposes, we'll create mock data
    // In real implementation, you would fetch from Instagram API or your backend
    const mockData = {
        username: username,
        profilePicUrl: generateMockProfilePic(),
        followerCount: generateMockFollowerCount()
    };
    
    // Update profile info
    profileUsername.textContent = mockData.username;
    profilePic.src = mockData.profilePicUrl;
    followerCount.textContent = formatFollowerCount(mockData.followerCount);
    
    return mockData;
}

function generateMockProfilePic() {
    // Generate a random profile picture using a service like Unsplash
    const randomId = Math.floor(Math.random() * 1000);
    return `https://picsum.photos/150/150?random=${randomId}`;
}

function generateMockFollowerCount() {
    // Generate a random follower count between 100 and 100000
    return Math.floor(Math.random() * 99900) + 100;
}

function formatFollowerCount(count) {
    if (count >= 1000000) {
        return (count / 1000000).toFixed(1) + 'M';
    } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'K';
    }
    return count.toString();
}

function showStep2() {
    step1.classList.remove('active');
    step2.classList.add('active');
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
    // Create and show error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}

function validateFollowerAmount() {
    const amount = parseInt(followerAmountInput.value);
    
    if (amount >= 100 && amount <= 5000) {
        proceedBtn.classList.remove('disabled');
        proceedBtn.disabled = false;
    } else {
        proceedBtn.classList.add('disabled');
        proceedBtn.disabled = true;
    }
}

function proceedToNextStep() {
    const amount = parseInt(followerAmountInput.value);
    
    if (amount >= 100 && amount <= 5000) {
        // Here you would proceed to the next step
        // For now, we'll show a success message
        showSuccess(`${amount} takipçi için işlem başlatılıyor...`);
    }
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    successDiv.textContent = message;
    
    document.body.appendChild(successDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Input validation for username
usernameInput.addEventListener('input', (e) => {
    // Remove any characters that aren't allowed in Instagram usernames
    e.target.value = e.target.value.replace(/[^a-zA-Z0-9._]/g, '');
});

// Auto-focus on username input when page loads
window.addEventListener('load', () => {
    usernameInput.focus();
});
