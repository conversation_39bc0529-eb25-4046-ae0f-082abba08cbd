// DOM Elements
const step1 = document.getElementById('step1');
const step2 = document.getElementById('step2');
const step3 = document.getElementById('step3');
const usernameInput = document.getElementById('username');
const checkProfileBtn = document.getElementById('checkProfile');
const loadingOverlay = document.getElementById('loadingOverlay');
const profileUsername = document.getElementById('profileUsername');
const followerAmountInput = document.getElementById('followerAmount');
const proceedBtn = document.getElementById('proceedBtn');
const quickBtns = document.querySelectorAll('.quick-btn');

// Event Listeners
checkProfileBtn.addEventListener('click', checkProfile);
usernameInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        checkProfile();
    }
});

followerAmountInput.addEventListener('input', validateFollowerAmount);

quickBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const amount = btn.dataset.amount;
        followerAmountInput.value = amount;
        
        // Remove active class from all buttons
        quickBtns.forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        btn.classList.add('active');
        
        validateFollowerAmount();
    });
});

proceedBtn.addEventListener('click', proceedToNextStep);

// Functions
async function checkProfile() {
    const username = usernameInput.value.trim();
    
    if (!username) {
        showError('Lütfen kullanıcı adınızı girin');
        return;
    }
    
    if (username.length < 3) {
        showError('Kullanıcı adı en az 3 karakter olmalıdır');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    try {
        // Simulate API call to get Instagram profile data
        await simulateProfileFetch(username);
        
        // Hide loading
        showLoading(false);
        
        // Show step 2
        showStep2();
        
    } catch (error) {
        showLoading(false);
        showError('Profil bulunamadı. Lütfen kullanıcı adını kontrol edin.');
    }
}

async function simulateProfileFetch(username) {
    // Simulate network delay for realistic experience
    await new Promise(resolve => setTimeout(resolve, 1500));

    // For any username, create a simple profile
    const profile = {
        username: username
    };

    // Update profile info - only username
    profileUsername.textContent = profile.username;

    return profile;
}

function getRandomColor() {
    const colors = ['e6683c', 'f09433', 'dc2743', 'cc2366', 'bc1888', '4CAF50', '2196F3', '9C27B0', 'FF5722'];
    return colors[Math.floor(Math.random() * colors.length)];
}

function generateRealisticFollowerCount() {
    const ranges = [
        { min: 1000, max: 5000, suffix: 'K', weight: 30 },
        { min: 5000, max: 15000, suffix: 'K', weight: 25 },
        { min: 15000, max: 50000, suffix: 'K', weight: 20 },
        { min: 50000, max: 100000, suffix: 'K', weight: 15 },
        { min: 100000, max: 500000, suffix: 'K', weight: 8 },
        { min: 1000000, max: 5000000, suffix: 'M', weight: 2 }
    ];

    // Weighted random selection
    const totalWeight = ranges.reduce((sum, range) => sum + range.weight, 0);
    let random = Math.random() * totalWeight;

    for (const range of ranges) {
        random -= range.weight;
        if (random <= 0) {
            const count = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;

            if (range.suffix === 'K') {
                return (count / 1000).toFixed(1) + 'K';
            } else if (range.suffix === 'M') {
                return (count / 1000000).toFixed(1) + 'M';
            }
            return count.toString();
        }
    }

    return '2.5K'; // fallback
}



function showStep2() {
    step1.classList.remove('active');
    step2.classList.add('active');
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
    // Create and show error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}

function validateFollowerAmount() {
    const amount = parseInt(followerAmountInput.value);

    if (amount >= 100 && amount <= 50000) {
        proceedBtn.classList.remove('disabled');
        proceedBtn.disabled = false;
    } else {
        proceedBtn.classList.add('disabled');
        proceedBtn.disabled = true;
    }
}

function proceedToNextStep() {
    const amount = parseInt(followerAmountInput.value);

    if (amount >= 100 && amount <= 50000) {
        // Show step 3
        showStep3();
        // Start countdown
        startCountdown();
    }
}

function showStep3() {
    step2.classList.remove('active');
    step3.classList.add('active');
}

function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 1001;
        animation: slideInRight 0.3s ease-out;
    `;
    successDiv.textContent = message;
    
    document.body.appendChild(successDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Input validation for username
usernameInput.addEventListener('input', (e) => {
    // Remove any characters that aren't allowed in Instagram usernames
    e.target.value = e.target.value.replace(/[^a-zA-Z0-9._]/g, '');
});

// Mock activity data for live cards
const mockActivityData = [
    { username: 'Ahmet K.', action: '+25K takipçi aldı', amount: '2 dk önce', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=70&h=70&fit=crop&crop=face' },
    { username: 'Zeynep Y.', action: '+18K takipçi aldı', amount: '5 dk önce', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=70&h=70&fit=crop&crop=face' },
    { username: 'Mahmut H.', action: '+32K takipçi aldı', amount: '8 dk önce', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=70&h=70&fit=crop&crop=face' },
    { username: 'Ayşe M.', action: '+15K takipçi aldı', amount: '12 dk önce', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=70&h=70&fit=crop&crop=face' },
    { username: 'Mustafa Ö.', action: '+41K takipçi aldı', amount: '15 dk önce', avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=70&h=70&fit=crop&crop=face' },
    { username: 'Fatma S.', action: '+22K takipçi aldı', amount: '18 dk önce', avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=70&h=70&fit=crop&crop=face' },
    { username: 'Ali R.', action: '+28K takipçi aldı', amount: '22 dk önce', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=70&h=70&fit=crop&crop=face' },
    { username: 'Elif T.', action: '+37K takipçi aldı', amount: '25 dk önce', avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=70&h=70&fit=crop&crop=face' },
    { username: 'Mehmet A.', action: '+12K takipçi aldı', amount: '28 dk önce', avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=70&h=70&fit=crop&crop=face' },
    { username: 'Selin K.', action: '+21K takipçi aldı', amount: '32 dk önce', avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=70&h=70&fit=crop&crop=face' },
    { username: 'Emre D.', action: '+50K takipçi aldı', amount: '35 dk önce', avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=70&h=70&fit=crop&crop=face' },
    { username: 'Büşra L.', action: '+19K takipçi aldı', amount: '38 dk önce', avatar: 'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=70&h=70&fit=crop&crop=face' },
    { username: 'Oğuz B.', action: '+34K takipçi aldı', amount: '42 dk önce', avatar: 'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?w=70&h=70&fit=crop&crop=face' },
    { username: 'Deniz C.', action: '+27K takipçi aldı', amount: '45 dk önce', avatar: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=70&h=70&fit=crop&crop=face' },
    { username: 'Gizem P.', action: '+16K takipçi aldı', amount: '48 dk önce', avatar: 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=70&h=70&fit=crop&crop=face' },
    { username: 'Burak G.', action: '+43K takipçi aldı', amount: '52 dk önce', avatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=70&h=70&fit=crop&crop=face' },
    { username: 'Merve N.', action: '+29K takipçi aldı', amount: '55 dk önce', avatar: 'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=70&h=70&fit=crop&crop=face' },
    { username: 'Cem V.', action: '+38K takipçi aldı', amount: '58 dk önce', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=70&h=70&fit=crop&crop=face' },
    { username: 'Pınar Z.', action: '+14K takipçi aldı', amount: '1 sa önce', avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=70&h=70&fit=crop&crop=face' },
    { username: 'Kaan F.', action: '+46K takipçi aldı', amount: '1 sa önce', avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=70&h=70&fit=crop&crop=face' }
];

let currentActivityIndex = 0;
let activityInterval;

function createActivityCard(data) {
    return `
        <div class="activity-card">
            <img src="${data.avatar}" alt="${data.username}" class="card-avatar">
            <div class="card-info">
                <div class="card-username">${data.username}</div>
                <div class="card-action">${data.action}</div>
            </div>
            <div class="card-amount">${data.amount}</div>
        </div>
    `;
}

function updateActivityCards() {
    const step1Container = document.getElementById('step1ActivityCards');
    const step2Container = document.getElementById('activityCards');
    const step3Container = document.getElementById('step3ActivityCards');

    // Get next 3 cards
    const cards = [];
    for (let i = 0; i < 3; i++) {
        const index = (currentActivityIndex + i) % mockActivityData.length;
        cards.push(createActivityCard(mockActivityData[index]));
    }

    const cardsHTML = cards.join('');

    // Update step 1 container if exists
    if (step1Container) {
        step1Container.style.opacity = '0';
        setTimeout(() => {
            step1Container.innerHTML = cardsHTML;
            step1Container.style.opacity = '1';
        }, 300);
    }

    // Update step 2 container if exists
    if (step2Container) {
        step2Container.style.opacity = '0';
        setTimeout(() => {
            step2Container.innerHTML = cardsHTML;
            step2Container.style.opacity = '1';
        }, 300);
    }

    // Update step 3 container if exists
    if (step3Container) {
        step3Container.style.opacity = '0';
        setTimeout(() => {
            step3Container.innerHTML = cardsHTML;
            step3Container.style.opacity = '1';
        }, 300);
    }

    // Move to next set
    currentActivityIndex = (currentActivityIndex + 3) % mockActivityData.length;
}

function startActivityAnimation() {
    // Initial load
    updateActivityCards();

    // Update every 6 seconds
    activityInterval = setInterval(updateActivityCards, 6000);
}

function stopActivityAnimation() {
    if (activityInterval) {
        clearInterval(activityInterval);
        activityInterval = null;
    }
}

// Countdown functionality
let countdownInterval;
let timeLeft = 600; // 10 minutes in seconds

function startCountdown() {
    const countdownElement = document.getElementById('countdown');

    countdownInterval = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;

        countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        timeLeft--;

        if (timeLeft < 0) {
            clearInterval(countdownInterval);
            countdownElement.textContent = '0:00';
        }
    }, 1000);
}

function stopCountdown() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }
}

// Auto-focus on username input when page loads
window.addEventListener('load', () => {
    usernameInput.focus();

    // Start activity animation immediately when page loads
    setTimeout(startActivityAnimation, 1000);

    // Also start activity animation when step 2 is shown (if not already running)
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.target.classList.contains('active') && mutation.target.id === 'step2') {
                if (!activityInterval) {
                    setTimeout(startActivityAnimation, 500);
                }
            }
        });
    });

    observer.observe(step2, { attributes: true, attributeFilter: ['class'] });
});
